import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-6 relative z-10">
      <div className="max-w-6xl mx-auto px-5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
        {/* TRYPINDIA Section */}
        <div className="space-y-5">
          <h3 className="text-2xl font-bold font-serif">
            TRYP
            <span className='text-blue-500'>INDIA</span>
          </h3>
          <p className="text-gray-300 leading-relaxed">
            Your trusted travel partner for exploring the beauty of North East
            India. We create unforgettable travel experiences.
          </p>
          <div className="flex gap-4">
            <a
              href="https://facebook.com/trypindia"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Visit our Facebook page"
              className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white text-xl transition-all duration-300 hover:-translate-y-1 hover:scale-110 hover:shadow-lg relative overflow-hidden border-2 border-transparent"
            >
              <i className="fab fa-facebook-f"></i>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 hover:opacity-100 hover:animate-shine"></span>
            </a>
            <a
              href="https://x.com/trypindia"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Visit our X (Twitter) page"
              className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white text-xl transition-all duration-300 hover:-translate-y-1 hover:scale-110 hover:shadow-lg relative overflow-hidden border-2 border-transparent"
            >
              <i className="fab fa-x-twitter"></i>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 hover:opacity-100 hover:animate-shine"></span>
            </a>
            <a
              href="https://instagram.com/trypindia"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Visit our Instagram page"
              className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white text-xl transition-all duration-300 hover:-translate-y-1 hover:scale-110 hover:shadow-lg relative overflow-hidden border-2 border-transparent"
            >
              <i className="fab fa-instagram"></i>
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 hover:opacity-100 hover:animate-shine"></span>
            </a>
          </div>
        </div>

        {/* Quick Links Section */}
        <div className="space-y-5">
          <h4 className="text-xl font-semibold relative inline-block pb-3 mb-2 font-serif">
            Quick Links
            <span className="absolute left-0 bottom-0 w-12 h-0.5 bg-blue-500"></span>
          </h4>
          <ul className="space-y-3">
            <li>
              <Link
                to="/"
                className="text-gray-300 hover:text-blue-400 transition-colors duration-300 hover:pl-2 block font-serif"
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                to="/plan"
                className="text-gray-300 hover:text-blue-400 transition-colors duration-300 hover:pl-2 block font-serif"
              >
                Packages
              </Link>
            </li>
            <li>
              <Link
                to="/about"
                className="text-gray-300 hover:text-blue-400 transition-colors duration-300 hover:pl-2 block font-serif"
              >
                About Us
              </Link>
            </li>
            <li>
              <Link
                to="#"     //fill up later
                className="text-gray-300 hover:text-blue-400 transition-colors duration-300 hover:pl-2 block font-serif"
              >
                Terms & Conditions
              </Link>
            </li>
            <li>
              <Link
                to="/contact"
                className="text-gray-300 hover:text-blue-400 transition-colors duration-300 hover:pl-2 block font-serif"
              >
                Contact
              </Link>
            </li>
          </ul>
        </div>

        {/* Contact Info Section */}
        <div className="space-y-5">
          <h4 className="text-xl font-semibold font-serif relative inline-block pb-3 mb-2">
            Contact Info
            <span className="absolute left-0 bottom-0 w-12 h-0.5 bg-blue-500"></span>
          </h4>
          <ul className="space-y-4">
            <li className="flex items-center gap-3 text-gray-300">
              <i className="fas fa-phone text-blue-500"></i>
              <span>+91 8584807189</span>
            </li>
            <li className="flex items-center gap-3 text-gray-300">
              <i className="fas fa-envelope text-blue-500"></i>
              <span className="font-serif"><EMAIL></span>
            </li>
          </ul>
        </div>

        {/* Instagram Feed Section */}
        <div className="space-y-5">
          <h4 className="text-xl font-semibold relative inline-block pb-3 mb-2 font-serif">
            Instagram Feed
            <span className="absolute left-0 bottom-0 w-12 h-0.5 bg-blue-400"></span>
          </h4>
          <p className="text-gray-300 font-serif">
            Follow us @trypindia for daily travel inspiration
          </p>

          {/* Instagram Grid */}
          <div className="grid grid-cols-3 gap-2 mt-4">
            <div className="aspect-square overflow-hidden rounded-lg group cursor-pointer">
              <img
                src="/src/assets/photos/dargeeling_tea.webp"
                alt="Instagram post 1"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="aspect-square overflow-hidden rounded-lg group cursor-pointer">
              <img
                src="/src/assets/photos/nature2.webp"
                alt="Instagram post 2"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="aspect-square overflow-hidden rounded-lg group cursor-pointer">
              <img
                src="/src/assets/photos/wildlife_redpanda.webp"
                alt="Instagram post 3"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="aspect-square overflow-hidden rounded-lg group cursor-pointer">
              <img
                src="/src/assets/photos/monks4.webp"
                alt="Instagram post 4"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="aspect-square overflow-hidden rounded-lg group cursor-pointer">
              <img
                src="/src/assets/photos/culture1.webp"
                alt="Instagram post 5"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
            <div className="aspect-square overflow-hidden rounded-lg group cursor-pointer">
              <img
                src="/src/assets/photos/attractions1.webp"
                alt="Instagram post 6"
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </div>
          </div>

          {/* Follow Button */}
          <a
            href="https://instagram.com/trypindia"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full text-sm font-medium transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/25 hover:-translate-y-0.5"
          >
            <i className="fab fa-instagram"></i>
            Follow @trypindia
          </a>
        </div>
      </div>

      {/* Footer Bottom */}
      <div className="mt-10 pt-6 border-t border-gray-800 text-center">
        <p className="text-gray-300 font-serif">© 2025 TRYPINDIA. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;